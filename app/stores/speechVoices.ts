import { defineStore } from 'pinia'
import type { SpeechVoice } from '~/composables/useSpeechVoices'

export const useSpeechVoicesStore = defineStore('speechVoices', {
  state: () => ({
    voices: [] as SpeechVoice[],
    selectedVoice: null as SpeechVoice | null,
    loading: false,
    error: null as string | null,
    updatings: {} as Record<string, boolean>,
    isInitialized: false
  }),

  persist: [
    {
      pick: ['selectedVoice'],
      storage: localStorage
    }
  ],

  getters: {
    favoriteVoices: (state) => state.voices.filter(voice => voice.is_favorite),
    
    voicesByGender: (state) => {
      const grouped: Record<string, SpeechVoice[]> = {}
      state.voices.forEach(voice => {
        if (!grouped[voice.gender]) {
          grouped[voice.gender] = []
        }
        grouped[voice.gender]!.push(voice)
      })
      return grouped
    },

    premiumVoices: (state) => state.voices.filter(voice => voice.premium),
    
    freeVoices: (state) => state.voices.filter(voice => !voice.premium)
  },

  actions: {
    async loadVoices(forceReload = false) {
      // Skip loading if voices already exist and not forcing reload
      if (this.voices.length > 0 && !forceReload && this.isInitialized) {
        return
      }

      // Skip if already loading
      if (this.loading) {
        return
      }

      this.loading = true
      this.error = null

      try {
        const { apiService } = useAPI()
        const response = await apiService.get('/voice-library/all')
        this.voices = response.data?.result || []
        this.isInitialized = true

        // Set default voice if none selected
        if (!this.selectedVoice && this.voices.length > 0) {
          this.selectedVoice = this.voices[0]!
        }
      } catch (err: any) {
        console.error('Failed to load speech voices:', err)
        this.error = err?.message || 'Failed to load voices'
      } finally {
        this.loading = false
      }
    },

    selectVoice(voice: SpeechVoice) {
      this.selectedVoice = voice
    },

    async toggleFavorite(voiceId: string, addFavorite: boolean) {
      const voice = this.voices.find(v => v.id === voiceId)
      if (!voice) return

      this.updatings[voiceId] = true
      
      try {
        const { apiService } = useAPI()
        if (addFavorite) {
          await apiService.post('/voice-library/add-favorite', {
            voice_id: voiceId
          })
        } else {
          await apiService.delete('/voice-library/remove-favorite', {
            data: { voice_id: voiceId }
          })
        }

        voice.is_favorite = !voice.is_favorite
      } catch (err: any) {
        console.error('Failed to toggle favorite:', err)
      } finally {
        this.updatings[voiceId] = false
      }
    },

    // Initialize store - load voices if not loaded
    async initialize() {
      if (!this.isInitialized && this.voices.length === 0) {
        await this.loadVoices()
      }
    }
  }
})
