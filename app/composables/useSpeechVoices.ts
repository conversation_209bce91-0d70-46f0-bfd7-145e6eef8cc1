export interface SpeechVoice {
  id: string
  type: string
  used_credit: number
  speaker_name: string
  premium: boolean
  created_at: string
  gender: string
  training_type: string
  updated_at: string
  age: string
  user_id: string | null
  accent: string
  user_model_path: string | null
  description: string
  embedding_path: string | null
  audio_path: string | null
  status: number
  speaker_id: number
  sample_audio_path: string
  training_status: string | null
  is_favorite: boolean
}

export function useSpeechVoices() {
  const store = useSpeechVoicesStore()

  // Initialize store on first use
  onMounted(() => {
    store.initialize()
  })

  return {
    voices: computed(() => store.voices),
    selectedVoice: computed({
      get: () => store.selectedVoice,
      set: value => store.selectedVoice = value
    }),
    loading: computed(() => store.loading),
    error: computed(() => store.error),
    loadVoices: store.loadVoices,
    selectVoice: store.selectVoice,
    toggleFavorite: store.toggleFavorite,
    favoriteVoices: computed(() => store.favoriteVoices),
    voicesByGender: computed(() => store.voicesByGender),
    premiumVoices: computed(() => store.premiumVoices),
    freeVoices: computed(() => store.freeVoices),
    updatings: computed(() => store.updatings)
  }
}
