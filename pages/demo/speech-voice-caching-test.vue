<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="text-center">
      <h1 class="text-3xl font-bold mb-4">Speech Voice Caching Test</h1>
      <p class="text-gray-600 dark:text-gray-400">
        Test để kiểm tra việc cache voices giữa các components khác nhau
      </p>
    </div>

    <!-- Store Status -->
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
      <h3 class="font-semibold mb-2">Store Status:</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span class="font-medium">Voices Count:</span>
          <span class="ml-2">{{ voices.length }}</span>
        </div>
        <div>
          <span class="font-medium">Loading:</span>
          <span class="ml-2" :class="loading ? 'text-orange-600' : 'text-green-600'">
            {{ loading ? 'Yes' : 'No' }}
          </span>
        </div>
        <div>
          <span class="font-medium">Initialized:</span>
          <span class="ml-2" :class="store.isInitialized ? 'text-green-600' : 'text-red-600'">
            {{ store.isInitialized ? 'Yes' : 'No' }}
          </span>
        </div>
        <div>
          <span class="font-medium">Selected:</span>
          <span class="ml-2">{{ selectedVoice?.speaker_name || 'None' }}</span>
        </div>
      </div>
    </div>

    <!-- Multiple Components Test -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Component 1 -->
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border">
        <h3 class="font-semibold mb-3">Component 1 - Modal</h3>
        <BaseSpeechVoiceSelectModal
          v-model="voice1"
          placeholder="Select voice 1"
          size="sm"
        />
        <div v-if="voice1" class="mt-2 text-xs text-gray-600">
          Selected: {{ voice1.speaker_name }} ({{ voice1.accent }})
        </div>
      </div>

      <!-- Component 2 -->
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border">
        <h3 class="font-semibold mb-3">Component 2 - Modal (Gemini Only)</h3>
        <BaseSpeechVoiceSelectModal
          v-model="voice2"
          placeholder="Select Gemini voice"
          :gemini-only="true"
          size="sm"
        />
        <div v-if="voice2" class="mt-2 text-xs text-gray-600">
          Selected: {{ voice2.speaker_name }} ({{ voice2.accent }})
        </div>
      </div>

      <!-- Component 3 -->
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border">
        <h3 class="font-semibold mb-3">Component 3 - Regular Select</h3>
        <BaseSpeechVoiceSelect
          v-model="voice3"
          placeholder="Select voice 3"
          size="sm"
        />
        <div v-if="voice3" class="mt-2 text-xs text-gray-600">
          Selected: {{ voice3.speaker_name }} ({{ voice3.accent }})
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex flex-wrap gap-4">
      <UButton
        @click="forceReload"
        :loading="loading"
        color="orange"
        variant="outline"
      >
        Force Reload Voices
      </UButton>
      
      <UButton
        @click="clearSelections"
        color="red"
        variant="outline"
      >
        Clear All Selections
      </UButton>

      <UButton
        @click="logStoreState"
        color="blue"
        variant="outline"
      >
        Log Store State
      </UButton>
    </div>

    <!-- Instructions -->
    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
      <h3 class="font-semibold mb-2">Test Instructions:</h3>
      <ol class="list-decimal list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
        <li>Mở Component 1 - sẽ load voices lần đầu</li>
        <li>Mở Component 2 hoặc 3 - sẽ sử dụng cache, không load lại</li>
        <li>Refresh page và mở bất kỳ component nào - sẽ load lại</li>
        <li>Kiểm tra Network tab để xác nhận API calls</li>
        <li>Selected voice sẽ được persist qua localStorage</li>
      </ol>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SpeechVoice } from '~/composables/useSpeechVoices'

// Get store and composable
const store = useSpeechVoicesStore()
const { voices, loading, selectedVoice, loadVoices } = useSpeechVoices()

// Local voice selections for each component
const voice1 = ref<SpeechVoice | null>(null)
const voice2 = ref<SpeechVoice | null>(null)
const voice3 = ref<SpeechVoice | null>(null)

// Actions
const forceReload = () => {
  loadVoices(true)
}

const clearSelections = () => {
  voice1.value = null
  voice2.value = null
  voice3.value = null
  store.selectedVoice = null
}

const logStoreState = () => {
  console.log('Store State:', {
    voicesCount: store.voices.length,
    isInitialized: store.isInitialized,
    loading: store.loading,
    error: store.error,
    selectedVoice: store.selectedVoice?.speaker_name
  })
}

// Initialize on mount
onMounted(() => {
  console.log('Page mounted, store will auto-initialize if needed')
})
</script>
